import asyncio
import json
import argparse
from pathlib import Path
import aiohttp
import openai
import google.generativeai as genai
from typing import List, Dict, Any, Optional, Union
import logging
from tqdm.asyncio import tqdm
import time
import random

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# OpenAI HTTP 요청 로그 비활성화
# logging.getLogger("openai").setLevel(logging.WARNING)
# logging.getLogger("httpx").setLevel(logging.WARNING)


class QueryRewriter:
    def __init__(self, api_key: str, model_type: str = "gpt", batch_size: int = 10):
        """
        한국어 쿼리 리라이팅 도구 초기화
        
        Args:
            api_key: API 키
            model_type: "gpt" 또는 "gemini"
            batch_size: 배치 크기
        """
        self.api_key = api_key
        self.model_type = model_type.lower()
        self.batch_size = batch_size
        
        # 한국어 쿼리 리라이팅 스타일별 프롬프트
        self.rewriting_styles = {
            "natural": {
                "name": "자연스러운 표현",
                "prompt": "다음 질문을 자연스럽고 명확한 한국어로 다시 작성해주세요. 의미는 그대로 유지하되, 더 자연스러운 표현을 사용해주세요. 질문만 반환하고 다른 설명은 하지 마세요."
            },
            "casual": {
                "name": "반말 (구어체)",
                "prompt": "다음 질문을 자연스러운 반말로 다시 작성해주세요. 친근하고 캐주얼한 톤을 사용하되, 질문의 의미는 그대로 유지해주세요. 예: '~해?', '~이야?', '~거야?'. 질문만 반환해주세요."
            },
            "formal_noun": {
                "name": "명사형 종결어미",
                "prompt": "다음 질문을 명사형 종결어미(~는지, ~하는지, ~인지)나 ~함, ~임 등을 사용하여 다시 작성해주세요. 격식 있지만 친근한 표현을 사용해주세요. 예: '~하는지', '~방법', '~방식'. 질문만 반환하세요."
            },
            "colloquial": {
                "name": "구어체",
                "prompt": "다음 질문을 일상 대화에서 사용하는 자연스러운 구어체로 다시 작성해주세요. 존댓말을 사용하되 딱딱하지 않게, 실제 대화에서 쓰일 법한 표현으로 만들어주세요. 질문만 반환하세요."
            }
        }
        
        if self.model_type == "gpt":
            openai.api_key = api_key
            self.client = openai.AsyncOpenAI(api_key=api_key)
        elif self.model_type == "gemini":
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('gemini-2.5-flash')
        else:
            raise ValueError("model_type은 'gpt' 또는 'gemini'이어야 합니다.")
    
    def get_random_style(self) -> tuple:
        """랜덤으로 리라이팅 스타일 선택"""
        style_key = random.choice(list(self.rewriting_styles.keys()))
        style_info = self.rewriting_styles[style_key]
        return style_key, style_info
    
    async def rewrite_with_gpt(self, text: str, max_retries: int = 5) -> tuple:
        """GPT를 사용한 쿼리 리라이팅 (최대 5번 재시도)"""
        for attempt in range(max_retries):
            try:
                style_key, style_info = self.get_random_style()
                prompt = style_info["prompt"]
                
                response = await self.client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "user", "content": f"{prompt}\n\n원본 질문: {text}"}
                    ],
                    temperature=0.7,
                    max_tokens=1000
                )

                # API 응답 검증
                if response and response.choices and len(response.choices) > 0:
                    rewritten_text = response.choices[0].message.content
                    if rewritten_text and rewritten_text.strip():
                        return rewritten_text.strip(), style_key
                    else:
                        logger.warning(f"GPT 리라이팅 결과가 비어있음 (시도 {attempt + 1}/{max_retries})")
                else:
                    logger.warning(f"GPT API 응답이 비어있음 (시도 {attempt + 1}/{max_retries})")

                # 재시도 전 대기 (지수 백오프)
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"GPT 리라이팅 재시도 대기 중: {wait_time:.2f}초")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                logger.error(f"GPT 리라이팅 오류 (시도 {attempt + 1}/{max_retries}): {e}")

                # 재시도 전 대기 (지수 백오프)
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"GPT 리라이팅 재시도 대기 중: {wait_time:.2f}초")
                    await asyncio.sleep(wait_time)

        # 모든 재시도 실패
        logger.error(f"GPT 리라이팅 최종 실패 (최대 {max_retries}번 시도)")
        return f"리라이팅 실패: 최대 재시도 횟수 초과", "failed"
    
    async def rewrite_with_gemini(self, text: str, max_retries: int = 5) -> tuple:
        """Gemini를 사용한 쿼리 리라이팅 (최대 5번 재시도)"""
        for attempt in range(max_retries):
            try:
                style_key, style_info = self.get_random_style()
                prompt = f"{style_info['prompt']}\n\n원본 질문: {text}"
                
                response = await asyncio.to_thread(self.model.generate_content, prompt)

                # API 응답 검증
                if response and hasattr(response, 'text') and response.text:
                    rewritten_text = response.text.strip()
                    if rewritten_text:
                        return rewritten_text, style_key
                    else:
                        logger.warning(f"Gemini 리라이팅 결과가 비어있음 (시도 {attempt + 1}/{max_retries})")
                else:
                    logger.warning(f"Gemini API 응답이 비어있음 (시도 {attempt + 1}/{max_retries})")

                # 재시도 전 대기 (지수 백오프)
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"Gemini 리라이팅 재시도 대기 중: {wait_time:.2f}초")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                logger.error(f"Gemini 리라이팅 오류 (시도 {attempt + 1}/{max_retries}): {e}")

                # 재시도 전 대기 (지수 백오프)
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"Gemini 리라이팅 재시도 대기 중: {wait_time:.2f}초")
                    await asyncio.sleep(wait_time)

        # 모든 재시도 실패
        logger.error(f"Gemini 리라이팅 최종 실패 (최대 {max_retries}번 시도)")
        return f"리라이팅 실패: 최대 재시도 횟수 초과", "failed"
    
    async def rewrite_text(self, text: str, max_retries: int = 5) -> tuple:
        """모델 타입에 따른 리라이팅 실행 (최대 5번 재시도)"""
        if self.model_type == "gpt":
            return await self.rewrite_with_gpt(text, max_retries)
        else:
            return await self.rewrite_with_gemini(text, max_retries)

    # --------------------------
    # 중첩 경로 유틸 함수 추가
    # --------------------------
    def get_value_by_path(self, item: Dict[str, Any], path: Union[str, List[Union[str, int]]]) -> Optional[Any]:
        if isinstance(path, str):
            return item.get(path)
        cur = item
        try:
            for key in path:
                if isinstance(key, int):
                    cur = cur[key]
                else:
                    cur = cur.get(key)
            return cur
        except Exception:
            return None

    def set_value_by_path(self, item: Dict[str, Any], path: Union[str, List[Union[str, int]]], value: Any):
        if isinstance(path, str):
            item[path] = value
            return
        cur = item
        for key in path[:-1]:
            if isinstance(key, int):
                cur = cur[key]
            else:
                if key not in cur or not isinstance(cur[key], (dict, list)):
                    # 최소한의 구조 보장
                    cur[key] = {}  # 예상 외 구조일 때 dict로 초기화
                cur = cur[key]
        last = path[-1]
        if isinstance(last, int):
            cur[last] = value
        else:
            cur[last] = value

    def find_user_content_path(self, data_item: Dict[str, Any]) -> Optional[List[Union[str, int]]]:
        """
        query_and_response 리스트에서 첫 번째 user의 content 경로를 찾아 반환
        예: ['query_and_response', 0, 'content']
        """
        if "query_and_response" in data_item and isinstance(data_item["query_and_response"], list):
            qnr = data_item["query_and_response"]
            for idx, turn in enumerate(qnr):
                if isinstance(turn, dict) and turn.get("from") == "user" and turn.get("content"):
                    return ["query_and_response", idx, "content"]
        return None

    def extract_text_for_rewriting(self, data_item: Dict[str, Any]) -> Optional[str]:
        """리라이팅할 텍스트 추출 (text 필드 우선, 없으면 query_and_response의 user content 등에서 찾기)"""
        # 일반적인 텍스트 필드명들
        text_fields = ['text', 'query', 'question', 'content', 'message', 'instruction', 'input']

        for field in text_fields:
            if field in data_item and data_item[field]:
                return str(data_item[field]).strip()

        # query_and_response 내 user content 사용
        path = self.find_user_content_path(data_item)
        if path is not None:
            val = self.get_value_by_path(data_item, path)
            if val:
                return str(val).strip()

        return None

    def find_text_field(self, data_item: Dict[str, Any]) -> Optional[Union[str, List[Union[str, int]]]]:
        """텍스트 필드명 또는 경로 찾기. top-level 문자열 키 또는 중첩 경로 리스트 반환"""
        text_fields = ['text', 'query', 'question', 'content', 'message', 'instruction', 'input']

        for field in text_fields:
            if field in data_item and data_item[field]:
                return field

        # query_and_response 내 user content 경로 반환
        path = self.find_user_content_path(data_item)
        if path is not None:
            return path

        return None

    async def rewrite_batch(self, batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """배치 단위로 리라이팅 수행"""
        all_results = []

        # 리라이팅할 항목 필터링
        filtered_batch = []
        for item in batch:
            text = self.extract_text_for_rewriting(item)
            if text:
                filtered_batch.append({
                    "original_item": item,
                    "text_to_rewrite": text,
                    "text_field": self.find_text_field(item)  # str 또는 List 경로
                })
            else:
                # 텍스트가 없는 경우 원본 그대로 추가
                logger.warning("리라이팅할 텍스트가 없는 항목 발견")
                item_copy = item.copy()
                item_copy["rewriting_status"] = "no_text_found"
                all_results.append(item_copy)

        if not filtered_batch:
            return all_results

        # 리라이팅 작업 생성 (최대 5번 재시도 적용)
        tasks = []
        for item in filtered_batch:
            task = self.rewrite_text(item["text_to_rewrite"], max_retries=5)
            tasks.append(task)

        # 리라이팅 결과 기다리기
        rewriting_results = await asyncio.gather(*tasks, return_exceptions=True)

        # 리라이팅된 항목들 처리
        for i, item in enumerate(filtered_batch):
            result = rewriting_results[i]
            original_item = item["original_item"].copy()
            text_field = item["text_field"]

            # 리라이팅 결과 검증 및 처리
            if text_field is not None and not isinstance(result, Exception):
                rewritten_text, style_key = result
                
                # 리라이팅 결과가 비어있거나 실패 메시지인지 확인
                if rewritten_text and not rewritten_text.startswith("리라이팅 실패:"):
                    # 원본 쿼리를 org_query에 저장 (top-level에 저장)
                    try:
                        org_val = self.get_value_by_path(original_item, text_field)
                    except Exception:
                        org_val = original_item.get(text_field, "")
                    original_item["org_query"] = org_val if org_val is not None else ""

                    # 리라이팅된 내용으로 업데이트 (중첩 경로 지원)
                    self.set_value_by_path(original_item, text_field, rewritten_text)
                    original_item["rewriting_style"] = style_key
                    original_item["rewriting_style_name"] = self.rewriting_styles[style_key]["name"]
                    original_item["rewriting_status"] = "success"
                    logger.debug(f"리라이팅 성공 ({style_key}): {item['text_to_rewrite'][:50]}... -> {rewritten_text[:50]}...")
                else:
                    # 리라이팅 실패 또는 빈 결과
                    original_item["rewriting_status"] = f"failed: {rewritten_text if rewritten_text else 'empty_result'}"
                    logger.warning(f"리라이팅 실패 또는 빈 결과: {item['text_to_rewrite'][:50]}...")
            else:
                # 예외 발생 또는 텍스트 필드 없음
                error_msg = str(result) if isinstance(result, Exception) else "no_text_field"
                original_item["rewriting_status"] = f"failed: {error_msg}"
                logger.error(f"리라이팅 처리 오류: {error_msg}")

            all_results.append(original_item)

        return all_results

    def save_batch_to_jsonl(self, batch: List[Dict[str, Any]], output_path: str, mode: str = "a"):
        """배치를 JSONL 파일로 저장"""
        with open(output_path, mode, encoding='utf-8') as f:
            for item in batch:
                f.write(json.dumps(item, ensure_ascii=False) + "\n")

    def load_data(self, input_path: str) -> List[Dict[str, Any]]:
        """JSONL 데이터 로드"""
        data = []
        with open(input_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    try:
                        data.append(json.loads(line))
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON 파싱 오류: {e}")
                        continue
        return data

    async def rewrite_dataset(
        self,
        input_path: str,
        output_path: str,
        start_id: int = 0
    ):
        """데이터셋 리라이팅"""
        # 데이터 로드
        logger.info(f"데이터 로딩 중: {input_path}")
        data = self.load_data(input_path)
        logger.info(f"총 {len(data)}개 항목 로드됨")

        # 리라이팅이 가능한 항목 수 계산
        rewriting_possible_count = 0
        for item in data:
            text = self.extract_text_for_rewriting(item)
            if text:
                rewriting_possible_count += 1

        logger.info(f"리라이팅 가능한 항목 수: {rewriting_possible_count}개 (전체 {len(data)}개 중)")

        # 리라이팅이 가능한 항목이 없으면 원본 데이터를 그대로 저장하고 종료
        if rewriting_possible_count == 0:
            logger.info("리라이팅 가능한 항목이 없습니다. 원본 데이터를 그대로 저장합니다.")
            with open(output_path, 'w', encoding='utf-8') as f:
                for item in data:
                    f.write(json.dumps(item, ensure_ascii=False) + "\n")
            logger.info(f"저장 완료: {output_path}")
            return

        # 시작 ID부터 처리
        if start_id > 0:
            data = data[start_id:]
            logger.info(f"시작 ID {start_id}부터 처리, 남은 항목: {len(data)}개")

        # 출력 파일 초기화 (새로 시작하는 경우)
        if start_id == 0:
            with open(output_path, 'w', encoding='utf-8') as f:
                pass  # 파일 비우기

        # 배치 단위로 처리
        total_batches = (len(data) + self.batch_size - 1) // self.batch_size
        processed_count = start_id
        rewritten_count = 0

        # 스타일 정보 출력
        logger.info("=== 리라이팅 스타일 정보 ===")
        for style_key, style_info in self.rewriting_styles.items():
            logger.info(f"- {style_key}: {style_info['name']}")

        logger.info(f"배치 크기: {self.batch_size}, 총 배치 수: {total_batches}")

        async for i in tqdm(range(0, len(data), self.batch_size), desc="리라이팅 진행"):
            batch = data[i:i + self.batch_size]

            try:
                # 배치 리라이팅
                rewritten_batch = await self.rewrite_batch(batch)

                if rewritten_batch:
                    # 배치 저장
                    self.save_batch_to_jsonl(rewritten_batch, output_path)
                    
                    # 성공적으로 리라이팅된 항목 수 계산
                    successful_rewrites = sum(1 for item in rewritten_batch 
                                            if item.get("rewriting_status") == "success")
                    rewritten_count += successful_rewrites

                processed_count += len(batch)
                logger.info(f"배치 완료: {processed_count}/{len(data) + start_id} 항목 처리됨, {rewritten_count}개 리라이팅됨")

                # API 제한을 위한 잠시 대기
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"배치 처리 오류 (ID {processed_count}): {e}")
                logger.info(f"다음 명령으로 재시작 가능: --start_id {processed_count}")
                break

        logger.info(f"리라이팅 완료! 총 {rewritten_count}개 항목이 한국어로 리라이팅되어 저장됨: {output_path}")


async def main():
    parser = argparse.ArgumentParser(description="한국어 쿼리 리라이팅 도구")
    parser.add_argument("--input_path", type=str, required=True,
                       help="입력 JSONL 파일 경로")
    parser.add_argument("--output_path", type=str, required=True,
                       help="출력 JSONL 파일 경로")
    parser.add_argument("--api_key", type=str, required=True,
                       help="API 키")
    parser.add_argument("--model_type", type=str, choices=["gpt", "gemini"],
                       default="gpt", help="모델 타입")
    parser.add_argument("--batch_size", type=int, default=10,
                       help="배치 크기")
    parser.add_argument("--start_id", type=int, default=0,
                       help="시작 ID (재시작 시 사용)")

    args = parser.parse_args()

    # 리라이터 초기화
    rewriter = QueryRewriter(
        api_key=args.api_key,
        model_type=args.model_type,
        batch_size=args.batch_size
    )

    # 리라이팅 실행
    await rewriter.rewrite_dataset(
        input_path=args.input_path,
        output_path=args.output_path,
        start_id=args.start_id
    )

if __name__ == "__main__":
    asyncio.run(main())
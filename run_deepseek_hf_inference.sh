#!/bin/bash

# DeepSeek v3.1 Hugging Face Math Inference Script
# This script runs inference on the math_500.jsonl dataset using DeepSeek v3.1 from Hugging Face

# Configuration
INPUT_FILE="math_500.jsonl"
OUTPUT_FILE="results/math_500_deepseek_v3.1_hf_responses.jsonl"
MODEL_NAME="deepseek-ai/DeepSeek-V3.1-Base"
DEVICE="auto"  # auto, cuda, cpu
START_ID=0
MAX_SAMPLES=""  # Leave empty for all samples, or set a number for testing
MAX_NEW_TOKENS=2048
TEMPERATURE=0.1

# Check if input file exists
if [ ! -f "$INPUT_FILE" ]; then
    echo "Error: Input file $INPUT_FILE not found"
    exit 1
fi

# Create results directory if it doesn't exist
mkdir -p results

echo "Starting DeepSeek v3.1 Hugging Face inference..."
echo "Input: $INPUT_FILE"
echo "Output: $OUTPUT_FILE"
echo "Model: $MODEL_NAME"
echo "Device: $DEVICE"
echo "Start ID: $START_ID"
echo "Max new tokens: $MAX_NEW_TOKENS"
echo "Temperature: $TEMPERATURE"
if [ -n "$MAX_SAMPLES" ]; then
    echo "Max samples: $MAX_SAMPLES"
else
    echo "Max samples: All"
fi
echo ""

# Build command
CMD="python deepseek_hf_inference.py \
    --input_path \"$INPUT_FILE\" \
    --output_path \"$OUTPUT_FILE\" \
    --model_name \"$MODEL_NAME\" \
    --device \"$DEVICE\" \
    --start_id $START_ID \
    --max_new_tokens $MAX_NEW_TOKENS \
    --temperature $TEMPERATURE"

# Add max_samples if specified
if [ -n "$MAX_SAMPLES" ]; then
    CMD="$CMD --max_samples $MAX_SAMPLES"
fi

# Run the inference
eval $CMD

echo ""
echo "Inference completed!"
echo "Results saved to: $OUTPUT_FILE"

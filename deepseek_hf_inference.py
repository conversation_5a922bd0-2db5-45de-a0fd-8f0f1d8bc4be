import json
import argparse
from pathlib import Path
import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
from typing import List, Dict, Any
import logging
from tqdm import tqdm
from datetime import datetime
import gc

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeepSeekHFInference:
    def __init__(self, model_name: str = "deepseek-ai/DeepSeek-V3.1-Base", device: str = "auto"):
        """
        Initialize DeepSeek inference with Hugging Face transformers
        
        Args:
            model_name: Hugging Face model name
            device: Device to use ("auto", "cuda", "cpu")
        """
        self.model_name = model_name
        self.device = device
        
        logger.info(f"Loading model: {model_name}")
        logger.info("This may take several minutes for large models...")
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_name, 
            trust_remote_code=True
        )
        
        # Load model with appropriate device mapping
        if device == "auto":
            # Let transformers handle device placement automatically
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                trust_remote_code=True,
                torch_dtype=torch.bfloat16,  # Use bfloat16 for efficiency
                device_map="auto"  # Automatically distribute across available GPUs
            )
        else:
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                trust_remote_code=True,
                torch_dtype=torch.bfloat16
            )
            if device != "cpu":
                self.model = self.model.to(device)
        
        logger.info(f"Model loaded successfully on device: {self.model.device}")
        
        # Set model to evaluation mode
        self.model.eval()
    
    def generate_response(self, user_query: str, max_new_tokens: int = 2048, temperature: float = 0.1) -> tuple:
        """
        Generate response for a single query using the loaded model
        
        Args:
            user_query: The math problem or question
            max_new_tokens: Maximum number of new tokens to generate
            temperature: Sampling temperature
            
        Returns:
            tuple: (response_text, success_flag, error_message)
        """
        try:
            # Prepare messages in chat format
            messages = [
                {
                    "role": "system", 
                    "content": "You are a helpful mathematics assistant. Solve the given math problem step by step, showing your work clearly. Provide a clear final answer."
                },
                {
                    "role": "user", 
                    "content": user_query
                }
            ]
            
            # Apply chat template and tokenize
            inputs = self.tokenizer.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt",
            ).to(self.model.device)
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    do_sample=temperature > 0,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                )
            
            # Decode only the new tokens (response)
            response_tokens = outputs[0][inputs["input_ids"].shape[-1]:]
            response_text = self.tokenizer.decode(response_tokens, skip_special_tokens=True)
            
            return response_text.strip(), True, None
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Generation failed: {error_msg}")
            return "", False, error_msg
    
    def process_dataset(self, input_path: str, output_path: str, start_id: int = 0, max_samples: int = None):
        """
        Process the entire dataset
        
        Args:
            input_path: Path to input JSONL file
            output_path: Path to output JSONL file
            start_id: Starting ID for resuming processing
            max_samples: Maximum number of samples to process (None for all)
        """
        logger.info(f"Starting dataset processing: {input_path} -> {output_path}")
        logger.info(f"Model: {self.model_name}, Start ID: {start_id}")
        
        # Load data
        data = self.load_data(input_path)
        logger.info(f"Loaded {len(data)} samples")
        
        # Filter data from start_id if resuming
        if start_id > 0:
            data = [item for i, item in enumerate(data) if i >= start_id]
            logger.info(f"Resuming from ID {start_id}, processing {len(data)} remaining samples")
        
        # Limit samples if specified
        if max_samples is not None:
            data = data[:max_samples]
            logger.info(f"Limited to {max_samples} samples")
        
        # Initialize output file
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        if start_id == 0:
            # Clear file if starting fresh
            with open(output_path, 'w', encoding='utf-8') as f:
                pass
        
        # Process samples one by one with progress bar
        processed_count = 0
        success_count = 0
        
        for i, item in enumerate(tqdm(data, desc="Processing samples")):
            try:
                # Extract user query from the conversation
                user_query = ""
                for msg in item.get("query_and_response", []):
                    if msg.get("from") == "user":
                        user_query = msg.get("content", "")
                        break
                
                if not user_query:
                    logger.warning(f"No user query found in item {item.get('id', 'unknown')}")
                    self.save_item_to_jsonl(item, output_path)
                    processed_count += 1
                    continue
                
                # Generate response
                response_text, success, error = self.generate_response(user_query)
                
                # Create updated item with response
                updated_item = item.copy()
                
                # Update the assistant response in query_and_response
                for msg in updated_item["query_and_response"]:
                    if msg.get("from") == "assistant":
                        msg["content"] = response_text
                        msg["provider"] = f"deepseek-hf-{self.model_name.split('/')[-1]}"
                        break
                
                # Add metadata
                updated_item["inference_metadata"] = {
                    "model": self.model_name,
                    "success": success,
                    "error": error,
                    "timestamp": datetime.now().isoformat(),
                    "response_length": len(response_text),
                    "sample_index": start_id + i
                }
                
                # Save result
                self.save_item_to_jsonl(updated_item, output_path)
                
                processed_count += 1
                if success:
                    success_count += 1
                    logger.debug(f"Successfully processed sample {i+1}/{len(data)}")
                else:
                    logger.error(f"Failed to process sample {i+1}/{len(data)}: {error}")
                
                # Periodic cleanup to manage memory
                if (i + 1) % 10 == 0:
                    gc.collect()
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                
            except Exception as e:
                logger.error(f"Error processing sample {i+1}: {str(e)}")
                # Save original item without response to avoid data loss
                self.save_item_to_jsonl(item, output_path)
                processed_count += 1
        
        logger.info(f"Dataset processing completed!")
        logger.info(f"Processed: {processed_count} samples")
        logger.info(f"Successful: {success_count} samples ({success_count/processed_count*100:.1f}%)")
        logger.info(f"Results saved to: {output_path}")
    
    def load_data(self, input_path: str) -> List[Dict[str, Any]]:
        """Load JSONL data from file"""
        data = []
        with open(input_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    try:
                        item = json.loads(line)
                        data.append(item)
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON parsing error at line {line_num}: {e}")
                        continue
        return data
    
    def save_item_to_jsonl(self, item: Dict[str, Any], output_path: str):
        """Save single item to JSONL file"""
        with open(output_path, 'a', encoding='utf-8') as f:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")


def main():
    parser = argparse.ArgumentParser(description="DeepSeek v3.1 Hugging Face Math Inference Tool")
    parser.add_argument("--input_path", type=str, required=True,
                       help="Input JSONL file path")
    parser.add_argument("--output_path", type=str, required=True,
                       help="Output JSONL file path")
    parser.add_argument("--model_name", type=str, default="deepseek-ai/DeepSeek-V3.1",
                       help="Hugging Face model name")
    parser.add_argument("--device", type=str, default="auto",
                       help="Device to use (auto, cuda, cpu)")
    parser.add_argument("--start_id", type=int, default=0,
                       help="Starting sample index for resuming processing")
    parser.add_argument("--max_samples", type=int, default=None,
                       help="Maximum number of samples to process (for testing)")
    parser.add_argument("--max_new_tokens", type=int, default=2048,
                       help="Maximum number of new tokens to generate")
    parser.add_argument("--temperature", type=float, default=0.1,
                       help="Sampling temperature")
    
    args = parser.parse_args()
    
    # Initialize inference client
    inference_client = DeepSeekHFInference(
        model_name=args.model_name,
        device=args.device
    )
    
    # Process dataset
    inference_client.process_dataset(
        input_path=args.input_path,
        output_path=args.output_path,
        start_id=args.start_id,
        max_samples=args.max_samples
    )


if __name__ == "__main__":
    main()

import pandas as pd
import j<PERSON>

def read_jsonl_to_dataframe(file_path):
    """
    Read a JSONL file and convert it to a pandas DataFrame
    """
    data = []
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            data.append(json.loads(line.strip()))
    return pd.DataFrame(data)

def check_response_ends_with_think_tag(query_and_response):
    """
    Check if the assistant's response ends with </think> tag
    """
    # Find the assistant's response
    for item in query_and_response:
        if item.get('from') == 'assistant':
            content = item.get('content', '')
            # Check if content ends with </think> (allowing for whitespace)
            return content.strip().endswith('</think>')
    return False

# Load the data
df = read_jsonl_to_dataframe('math_500_response_generated.jsonl')

# Create dataset column
df['dataset'] = df['id'].apply(lambda x: x.split('_')[0])

# Apply the check to all samples
df['ends_with_think_tag'] = df['query_and_response'].apply(check_response_ends_with_think_tag)

# Count how many samples end with </think>
think_tag_count = df['ends_with_think_tag'].sum()
total_samples = len(df)

print(f"=== ANALYSIS OF RESPONSES ENDING WITH </think> TAG ===")
print(f"Total samples: {total_samples}")
print(f"Samples ending with </think> tag: {think_tag_count}")
print(f"Percentage: {think_tag_count/total_samples*100:.2f}%")

# Show breakdown by dataset
print(f"\n=== BREAKDOWN BY DATASET ===")
breakdown = df.groupby('dataset')['ends_with_think_tag'].agg(['count', 'sum']).reset_index()
breakdown['percentage'] = (breakdown['sum'] / breakdown['count'] * 100).round(2)
breakdown.columns = ['dataset', 'total_samples', 'think_tag_samples', 'percentage']

for _, row in breakdown.iterrows():
    print(f"{row['dataset']}: {row['think_tag_samples']}/{row['total_samples']} ({row['percentage']}%)")

# Show some examples of responses that end with </think>
think_tag_samples = df[df['ends_with_think_tag'] == True]

print(f"\n=== EXAMPLES OF RESPONSES ENDING WITH </think> TAG ===")
print(f"Found {len(think_tag_samples)} samples")

# Show first few examples
for i, (idx, row) in enumerate(think_tag_samples.head(3).iterrows()):
    print(f"\n--- Example {i+1} ---")
    print(f"ID: {row['id']}")
    print(f"Dataset: {row['dataset']}")
    
    # Get the user's question
    user_question = ""
    assistant_response = ""
    for item in row['query_and_response']:
        if item.get('from') == 'user':
            user_question = item.get('content', '')
        elif item.get('from') == 'assistant':
            assistant_response = item.get('content', '')
    
    print(f"Question: {user_question[:100]}...")
    print(f"Response ending: ...{assistant_response[-200:]}")

# Additional analysis: Check for different think tag patterns
def check_contains_think_tags(query_and_response):
    """Check if response contains any think tags"""
    for item in query_and_response:
        if item.get('from') == 'assistant':
            content = item.get('content', '')
            return '<think>' in content or '</think>' in content
    return False

def check_has_incomplete_think(query_and_response):
    """Check if response has <think> but no </think>"""
    for item in query_and_response:
        if item.get('from') == 'assistant':
            content = item.get('content', '')
            return '<think>' in content and '</think>' not in content
    return False

def get_response_length(query_and_response):
    """Get the length of the assistant's response"""
    for item in query_and_response:
        if item.get('from') == 'assistant':
            content = item.get('content', '')
            return len(content)
    return 0

# Apply additional checks
df['contains_think_tags'] = df['query_and_response'].apply(check_contains_think_tags)
df['has_incomplete_think'] = df['query_and_response'].apply(check_has_incomplete_think)
df['response_length'] = df['query_and_response'].apply(get_response_length)

print(f"\n=== ADDITIONAL ANALYSIS ===")
print(f"Samples containing any think tags: {df['contains_think_tags'].sum()}")
print(f"Samples with incomplete think tags (<think> but no </think>): {df['has_incomplete_think'].sum()}")
print(f"Samples ending with </think> tag: {think_tag_count}")

# Length statistics for all samples
print(f"\n=== RESPONSE LENGTH STATISTICS (ALL SAMPLES) ===")
print(f"Total samples: {len(df)}")
print(f"Mean length: {df['response_length'].mean():.1f} characters")
print(f"Median length: {df['response_length'].median():.1f} characters")
print(f"Min length: {df['response_length'].min()} characters")
print(f"Max length: {df['response_length'].max()} characters")
print(f"Std deviation: {df['response_length'].std():.1f} characters")

# Length statistics by dataset
print(f"\n=== RESPONSE LENGTH BY DATASET ===")
length_stats = df.groupby('dataset')['response_length'].agg(['count', 'mean', 'median', 'min', 'max', 'std']).round(1)
for dataset, stats in length_stats.iterrows():
    print(f"{dataset}:")
    print(f"  Count: {stats['count']}")
    print(f"  Mean: {stats['mean']:.1f}, Median: {stats['median']:.1f}")
    print(f"  Min: {stats['min']:.0f}, Max: {stats['max']:.0f}")
    print(f"  Std: {stats['std']:.1f}")

if think_tag_count == 0:
    print(f"\n=== CONCLUSION ===")
    print(f"No samples end with </think> tag in this dataset.")
    print(f"This suggests that all responses in this dataset are complete.")
else:
    print(f"\n=== SAMPLES ENDING WITH </think> TAG ===")
    think_tag_samples = df[df['ends_with_think_tag'] == True]
    print(f"Length statistics for {think_tag_count} samples ending with </think>:")
    print(f"Mean length: {think_tag_samples['response_length'].mean():.1f} characters")
    print(f"Median length: {think_tag_samples['response_length'].median():.1f} characters")
    print(f"Min length: {think_tag_samples['response_length'].min()} characters")
    print(f"Max length: {think_tag_samples['response_length'].max()} characters")
    print(f"Std deviation: {think_tag_samples['response_length'].std():.1f} characters")

    print(f"\nThese {think_tag_count} samples appear to have incomplete responses that stop at the </think> tag.")
    print(f"This suggests the model's reasoning was cut off before generating the final answer.")

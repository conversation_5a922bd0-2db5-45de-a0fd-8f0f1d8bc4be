#!/usr/bin/env python3
"""
Test script for DeepSeek v3.1 inference on a small subset of math problems
"""

import json
import sys
from pathlib import Path

# Add current directory to path to import our module
sys.path.append('.')

from deepseek_hf_inference import DeepSeekHFInference

def create_test_subset(input_file: str, output_file: str, num_samples: int = 3):
    """Create a small test subset from the original dataset"""
    print(f"Creating test subset with {num_samples} samples...")
    
    data = []
    with open(input_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= num_samples:
                break
            if line.strip():
                data.append(json.loads(line))
    
    # Save test subset
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    print(f"Test subset saved to: {output_file}")
    return len(data)

def main():
    # Configuration
    original_file = "math_500.jsonl"
    test_input_file = "test_math_subset.jsonl"
    test_output_file = "results/test_deepseek_responses.jsonl"
    num_test_samples = 3
    
    print("=== DeepSeek v3.1 Test Inference ===")
    print()
    
    # Check if original file exists
    if not Path(original_file).exists():
        print(f"Error: {original_file} not found!")
        return
    
    # Create test subset
    actual_samples = create_test_subset(original_file, test_input_file, num_test_samples)
    print(f"Created test subset with {actual_samples} samples")
    print()
    
    # Create results directory
    Path("results").mkdir(exist_ok=True)
    
    try:
        print("Initializing DeepSeek v3.1 model...")
        print("Note: This may take several minutes for the first run...")
        
        # Initialize inference client
        inference_client = DeepSeekHFInference(
            model_name="deepseek-ai/DeepSeek-V3.1-Base",
            device="auto"
        )
        
        print("Model loaded successfully!")
        print()
        
        # Process test dataset
        print("Processing test samples...")
        inference_client.process_dataset(
            input_path=test_input_file,
            output_path=test_output_file,
            start_id=0,
            max_samples=None  # Process all samples in the test file
        )
        
        print()
        print("=== Test Results ===")
        
        # Load and display results
        results = []
        with open(test_output_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    results.append(json.loads(line))
        
        print(f"Processed {len(results)} samples")
        
        # Show sample results
        for i, result in enumerate(results):
            print(f"\n--- Sample {i+1} ---")
            print(f"ID: {result.get('id', 'unknown')}")
            
            # Get user question
            user_question = ""
            assistant_response = ""
            for msg in result.get("query_and_response", []):
                if msg.get("from") == "user":
                    user_question = msg.get("content", "")
                elif msg.get("from") == "assistant":
                    assistant_response = msg.get("content", "")
            
            print(f"Question: {user_question[:200]}...")
            print(f"Response length: {len(assistant_response)} characters")
            
            # Check if successful
            metadata = result.get("inference_metadata", {})
            success = metadata.get("success", False)
            print(f"Success: {success}")
            
            if success and assistant_response:
                print(f"Response preview: {assistant_response[:300]}...")
            elif not success:
                error = metadata.get("error", "Unknown error")
                print(f"Error: {error}")
        
        # Summary
        successful = sum(1 for r in results if r.get("inference_metadata", {}).get("success", False))
        print(f"\n=== Summary ===")
        print(f"Total samples: {len(results)}")
        print(f"Successful: {successful}")
        print(f"Success rate: {successful/len(results)*100:.1f}%")
        
        if successful > 0:
            print("\n✅ Test completed successfully!")
            print("You can now run the full inference on all 500 samples using:")
            print("bash run_deepseek_hf_inference.sh")
        else:
            print("\n❌ Test failed. Please check the errors above.")
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        print("Please check your GPU memory and model requirements.")
    
    finally:
        # Clean up test file
        if Path(test_input_file).exists():
            Path(test_input_file).unlink()
            print(f"Cleaned up test file: {test_input_file}")

if __name__ == "__main__":
    main()

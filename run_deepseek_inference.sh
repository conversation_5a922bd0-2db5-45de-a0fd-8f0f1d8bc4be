#!/bin/bash

# DeepSeek v3.1 Math Inference Script
# This script runs inference on the math_500.jsonl dataset using DeepSeek v3.1

# Configuration
INPUT_FILE="math_500.jsonl"
OUTPUT_FILE="results/math_500_deepseek_v3.1_responses.jsonl"
MODEL="deepseek-chat"  # or "deepseek-reasoner" for thinking mode
BATCH_SIZE=5
START_ID=0

# Check if API key is provided
if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "Error: DEEPSEEK_API_KEY environment variable is not set"
    echo "Please set your DeepSeek API key:"
    echo "export DEEPSEEK_API_KEY='your-api-key-here'"
    exit 1
fi

# Check if input file exists
if [ ! -f "$INPUT_FILE" ]; then
    echo "Error: Input file $INPUT_FILE not found"
    exit 1
fi

# Create results directory if it doesn't exist
mkdir -p results

echo "Starting DeepSeek v3.1 inference..."
echo "Input: $INPUT_FILE"
echo "Output: $OUTPUT_FILE"
echo "Model: $MODEL"
echo "Batch size: $BATCH_SIZE"
echo "Start ID: $START_ID"
echo ""

# Run the inference
python generate_response.py \
    --input_path "$INPUT_FILE" \
    --output_path "$OUTPUT_FILE" \
    --api_key "$DEEPSEEK_API_KEY" \
    --model "$MODEL" \
    --batch_size "$BATCH_SIZE" \
    --start_id "$START_ID"

echo ""
echo "Inference completed!"
echo "Results saved to: $OUTPUT_FILE"

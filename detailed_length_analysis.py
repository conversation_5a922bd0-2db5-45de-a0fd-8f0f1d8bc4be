import pandas as pd
import json
import matplotlib.pyplot as plt
import numpy as np

def read_jsonl_to_dataframe(file_path):
    """Read a JSONL file and convert it to a pandas DataFrame"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            data.append(json.loads(line.strip()))
    return pd.DataFrame(data)

def check_response_ends_with_think_tag(query_and_response):
    """Check if the assistant's response ends with </think> tag"""
    for item in query_and_response:
        if item.get('from') == 'assistant':
            content = item.get('content', '')
            return content.strip().endswith('</think>')
    return False

def get_response_length(query_and_response):
    """Get the length of the assistant's response"""
    for item in query_and_response:
        if item.get('from') == 'assistant':
            content = item.get('content', '')
            return len(content)
    return 0

# Load and prepare data
df = read_jsonl_to_dataframe('math_500_response_generated.jsonl')
df['dataset'] = df['id'].apply(lambda x: x.split('_')[0])
df['ends_with_think_tag'] = df['query_and_response'].apply(check_response_ends_with_think_tag)
df['response_length'] = df['query_and_response'].apply(get_response_length)

# Create completion status
df['completion_status'] = df['ends_with_think_tag'].map({True: 'Incomplete (ends with </think>)', False: 'Complete'})

print("=== COMPREHENSIVE RESPONSE LENGTH ANALYSIS ===\n")

# Overall statistics
total_samples = len(df)
incomplete_samples = df['ends_with_think_tag'].sum()
complete_samples = total_samples - incomplete_samples

print(f"OVERALL SUMMARY:")
print(f"Total samples: {total_samples}")
print(f"Complete responses: {complete_samples} ({complete_samples/total_samples*100:.1f}%)")
print(f"Incomplete responses (ending with </think>): {incomplete_samples} ({incomplete_samples/total_samples*100:.1f}%)")

# Length comparison between complete and incomplete
complete_df = df[df['ends_with_think_tag'] == False]
incomplete_df = df[df['ends_with_think_tag'] == True]

print(f"\nLENGTH COMPARISON:")
print(f"Complete responses:")
print(f"  Count: {len(complete_df)}")
print(f"  Mean: {complete_df['response_length'].mean():.0f} chars")
print(f"  Median: {complete_df['response_length'].median():.0f} chars")
print(f"  Range: {complete_df['response_length'].min()}-{complete_df['response_length'].max()} chars")

print(f"\nIncomplete responses (ending with </think>):")
print(f"  Count: {len(incomplete_df)}")
print(f"  Mean: {incomplete_df['response_length'].mean():.0f} chars")
print(f"  Median: {incomplete_df['response_length'].median():.0f} chars")
print(f"  Range: {incomplete_df['response_length'].min()}-{incomplete_df['response_length'].max()} chars")

# Dataset breakdown
print(f"\nDETAILED BREAKDOWN BY DATASET:")
dataset_stats = df.groupby(['dataset', 'completion_status']).agg({
    'response_length': ['count', 'mean', 'median', 'min', 'max']
}).round(0)

for dataset in df['dataset'].unique():
    dataset_df = df[df['dataset'] == dataset]
    complete_count = len(dataset_df[dataset_df['ends_with_think_tag'] == False])
    incomplete_count = len(dataset_df[dataset_df['ends_with_think_tag'] == True])
    
    print(f"\n{dataset}:")
    print(f"  Total: {len(dataset_df)} samples")
    print(f"  Complete: {complete_count} ({complete_count/len(dataset_df)*100:.1f}%)")
    print(f"  Incomplete: {incomplete_count} ({incomplete_count/len(dataset_df)*100:.1f}%)")
    
    if complete_count > 0:
        complete_subset = dataset_df[dataset_df['ends_with_think_tag'] == False]
        print(f"  Complete responses - Mean: {complete_subset['response_length'].mean():.0f}, Median: {complete_subset['response_length'].median():.0f}")
    
    if incomplete_count > 0:
        incomplete_subset = dataset_df[dataset_df['ends_with_think_tag'] == True]
        print(f"  Incomplete responses - Mean: {incomplete_subset['response_length'].mean():.0f}, Median: {incomplete_subset['response_length'].median():.0f}")

# Length distribution analysis
print(f"\nLENGTH DISTRIBUTION ANALYSIS:")
print(f"Percentiles for all responses:")
percentiles = [10, 25, 50, 75, 90, 95, 99]
for p in percentiles:
    print(f"  {p}th percentile: {np.percentile(df['response_length'], p):.0f} chars")

print(f"\nPercentiles for incomplete responses:")
if len(incomplete_df) > 0:
    for p in percentiles:
        print(f"  {p}th percentile: {np.percentile(incomplete_df['response_length'], p):.0f} chars")

# Create visualizations
plt.style.use('default')
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 1. Length distribution by completion status
axes[0, 0].hist([complete_df['response_length'], incomplete_df['response_length']], 
                bins=30, alpha=0.7, label=['Complete', 'Incomplete (ends with </think>)'])
axes[0, 0].set_xlabel('Response Length (characters)')
axes[0, 0].set_ylabel('Frequency')
axes[0, 0].set_title('Response Length Distribution by Completion Status')
axes[0, 0].legend()

# 2. Box plot by dataset (simplified without seaborn)
datasets = df['dataset'].unique()
complete_lengths = [df[(df['dataset']==d) & (df['ends_with_think_tag']==False)]['response_length'].values for d in datasets]
incomplete_lengths = [df[(df['dataset']==d) & (df['ends_with_think_tag']==True)]['response_length'].values for d in datasets]

positions = np.arange(len(datasets))
bp1 = axes[0, 1].boxplot(complete_lengths, positions=positions-0.2, widths=0.3, patch_artist=True)
bp2 = axes[0, 1].boxplot(incomplete_lengths, positions=positions+0.2, widths=0.3, patch_artist=True)

for patch in bp1['boxes']:
    patch.set_facecolor('lightblue')
for patch in bp2['boxes']:
    patch.set_facecolor('lightcoral')

axes[0, 1].set_xlabel('Dataset')
axes[0, 1].set_ylabel('Response Length (characters)')
axes[0, 1].set_title('Response Length by Dataset and Completion Status')
axes[0, 1].set_xticks(positions)
axes[0, 1].set_xticklabels([d.replace('WebInstructSub-prometheus', 'WebInstruct').replace('OpenMathInstruct-2', 'OpenMath') for d in datasets], rotation=45)
axes[0, 1].legend([bp1["boxes"][0], bp2["boxes"][0]], ['Complete', 'Incomplete'], loc='upper right')

# 3. Completion rate by dataset
completion_rates = df.groupby('dataset')['ends_with_think_tag'].agg(['count', 'sum']).reset_index()
completion_rates['incomplete_rate'] = completion_rates['sum'] / completion_rates['count'] * 100
completion_rates['dataset_short'] = completion_rates['dataset'].str.replace('WebInstructSub-prometheus', 'WebInstruct').str.replace('OpenMathInstruct-2', 'OpenMath')

axes[1, 0].bar(completion_rates['dataset_short'], completion_rates['incomplete_rate'])
axes[1, 0].set_xlabel('Dataset')
axes[1, 0].set_ylabel('Incomplete Response Rate (%)')
axes[1, 0].set_title('Percentage of Responses Ending with </think> by Dataset')
axes[1, 0].tick_params(axis='x', rotation=45)

# 4. Scatter plot: length vs completion status
axes[1, 1].scatter(complete_df.index, complete_df['response_length'], alpha=0.6, label='Complete', s=20)
axes[1, 1].scatter(incomplete_df.index, incomplete_df['response_length'], alpha=0.6, label='Incomplete', s=20)
axes[1, 1].set_xlabel('Sample Index')
axes[1, 1].set_ylabel('Response Length (characters)')
axes[1, 1].set_title('Response Length by Sample Index')
axes[1, 1].legend()

plt.tight_layout()
plt.savefig('response_length_analysis.png', dpi=300, bbox_inches='tight')
print(f"\nVisualization saved as 'response_length_analysis.png'")

print(f"\n=== KEY INSIGHTS ===")
print(f"1. {incomplete_samples/total_samples*100:.1f}% of responses are incomplete (end with </think>)")
print(f"2. Incomplete responses are significantly longer on average ({incomplete_df['response_length'].mean():.0f} vs {complete_df['response_length'].mean():.0f} chars)")
print(f"3. This suggests longer reasoning chains are more likely to be truncated")
print(f"4. OpenMathInstruct-2 has the highest incomplete rate ({df[df['dataset']=='OpenMathInstruct-2']['ends_with_think_tag'].mean()*100:.1f}%)")
print(f"5. lmsys-chat-1m has the lowest incomplete rate ({df[df['dataset']=='lmsys-chat-1m']['ends_with_think_tag'].mean()*100:.1f}%)")

import asyncio
import json
import argparse
from pathlib import Path
import openai
from typing import List, Dict, Any, Optional
import logging
from tqdm.asyncio import tqdm
import time
import random
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeepSeekInference:
    def __init__(self, api_key: str, model_name: str = "deepseek-chat", batch_size: int = 10):
        """
        Initialize DeepSeek inference client

        Args:
            api_key: DeepSeek API key
            model_name: Model name (deepseek-chat or deepseek-reasoner)
            batch_size: Number of concurrent requests
        """
        self.api_key = api_key
        self.model_name = model_name
        self.batch_size = batch_size

        # Initialize OpenAI client with DeepSeek endpoint
        self.client = openai.AsyncOpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )

        logger.info(f"Initialized DeepSeek client with model: {model_name}")

    async def generate_response(self, user_query: str, max_retries: int = 3) -> tuple:
        """
        Generate response for a single query using DeepSeek API

        Args:
            user_query: The math problem or question
            max_retries: Maximum number of retry attempts

        Returns:
            tuple: (response_text, success_flag, error_message)
        """
        for attempt in range(max_retries):
            try:
                response = await self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[
                        {
                            "role": "system",
                            "content": "You are a helpful mathematics assistant. Solve the given math problem step by step, showing your work clearly. Provide a clear final answer."
                        },
                        {
                            "role": "user",
                            "content": user_query
                        }
                    ],
                    temperature=0.1,  # Low temperature for consistent math solutions
                    max_tokens=4000,  # Sufficient for detailed math solutions
                    stream=False
                )

                if response and response.choices and len(response.choices) > 0:
                    response_text = response.choices[0].message.content
                    if response_text and response_text.strip():
                        return response_text.strip(), True, None
                    else:
                        logger.warning(f"Empty response received (attempt {attempt + 1}/{max_retries})")
                else:
                    logger.warning(f"Invalid API response structure (attempt {attempt + 1}/{max_retries})")

                # Wait before retry with exponential backoff
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"Retrying in {wait_time:.2f} seconds...")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                logger.error(f"API call failed (attempt {attempt + 1}/{max_retries}): {error_msg}")

                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"Retrying in {wait_time:.2f} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    return "", False, error_msg

        return "", False, "Max retries exceeded"

    async def process_batch(self, batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process a batch of samples concurrently

        Args:
            batch: List of sample dictionaries

        Returns:
            List of processed samples with responses
        """
        tasks = []
        for item in batch:
            # Extract user query from the conversation
            user_query = ""
            for msg in item.get("query_and_response", []):
                if msg.get("from") == "user":
                    user_query = msg.get("content", "")
                    break

            if user_query:
                task = self.generate_response(user_query)
                tasks.append((task, item))
            else:
                logger.warning(f"No user query found in item {item.get('id', 'unknown')}")
                tasks.append((None, item))

        # Execute all tasks concurrently
        results = []
        for task_info in tasks:
            task, original_item = task_info

            if task is None:
                # No user query found, keep original
                results.append(original_item)
                continue

            try:
                response_text, success, error = await task

                # Create updated item with response
                updated_item = original_item.copy()

                # Update the assistant response in query_and_response
                for msg in updated_item["query_and_response"]:
                    if msg.get("from") == "assistant":
                        msg["content"] = response_text
                        msg["provider"] = f"deepseek-{self.model_name}"
                        break

                # Add metadata
                updated_item["inference_metadata"] = {
                    "model": self.model_name,
                    "success": success,
                    "error": error,
                    "timestamp": datetime.now().isoformat(),
                    "response_length": len(response_text)
                }

                results.append(updated_item)

                if success:
                    logger.info(f"Successfully processed {original_item.get('id', 'unknown')}")
                else:
                    logger.error(f"Failed to process {original_item.get('id', 'unknown')}: {error}")

            except Exception as e:
                logger.error(f"Error processing item {original_item.get('id', 'unknown')}: {str(e)}")
                results.append(original_item)

        return results

    async def process_dataset(self, input_path: str, output_path: str, start_id: int = 0):
        """
        Process the entire dataset with batch processing and progress tracking

        Args:
            input_path: Path to input JSONL file
            output_path: Path to output JSONL file
            start_id: Starting ID for resuming processing
        """
        logger.info(f"Starting dataset processing: {input_path} -> {output_path}")
        logger.info(f"Model: {self.model_name}, Batch size: {self.batch_size}, Start ID: {start_id}")

        # Load data
        data = self.load_data(input_path)
        logger.info(f"Loaded {len(data)} samples")

        # Filter data from start_id if resuming
        if start_id > 0:
            data = [item for item in data if self.get_item_index(item) >= start_id]
            logger.info(f"Resuming from ID {start_id}, processing {len(data)} remaining samples")

        # Initialize output file
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        if start_id == 0:
            # Clear file if starting fresh
            with open(output_path, 'w', encoding='utf-8') as f:
                pass

        # Process in batches
        total_batches = (len(data) + self.batch_size - 1) // self.batch_size
        processed_count = 0

        for i in range(0, len(data), self.batch_size):
            batch = data[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1

            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} samples)")

            try:
                # Process batch
                processed_batch = await self.process_batch(batch)

                # Save batch results
                self.save_batch_to_jsonl(processed_batch, output_path, mode="a")

                processed_count += len(processed_batch)
                success_count = sum(1 for item in processed_batch
                                  if item.get("inference_metadata", {}).get("success", False))

                logger.info(f"Batch {batch_num} completed: {success_count}/{len(processed_batch)} successful")
                logger.info(f"Total progress: {processed_count}/{len(data)} samples processed")

                # Add delay between batches to respect rate limits
                if batch_num < total_batches:
                    await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"Error processing batch {batch_num}: {str(e)}")
                # Save original batch without responses to avoid data loss
                self.save_batch_to_jsonl(batch, output_path, mode="a")
                processed_count += len(batch)

        logger.info(f"Dataset processing completed! Processed {processed_count} samples")
        logger.info(f"Results saved to: {output_path}")

    def load_data(self, input_path: str) -> List[Dict[str, Any]]:
        """Load JSONL data from file"""
        data = []
        with open(input_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    try:
                        item = json.loads(line)
                        data.append(item)
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON parsing error at line {line_num}: {e}")
                        continue
        return data

    def save_batch_to_jsonl(self, batch: List[Dict[str, Any]], output_path: str, mode: str = "a"):
        """Save batch results to JSONL file"""
        with open(output_path, mode, encoding='utf-8') as f:
            for item in batch:
                f.write(json.dumps(item, ensure_ascii=False) + "\n")

    def get_item_index(self, item: Dict[str, Any]) -> int:
        """Extract index from item for resuming purposes"""
        # Try to extract index from ID or use a default
        item_id = item.get("id", "")
        try:
            # If ID contains numbers, extract the last number
            import re
            numbers = re.findall(r'\d+', item_id)
            if numbers:
                return int(numbers[-1])
        except:
            pass
        return 0


async def main():
    parser = argparse.ArgumentParser(description="DeepSeek v3.1 Math Inference Tool")
    parser.add_argument("--input_path", type=str, required=True,
                       help="Input JSONL file path")
    parser.add_argument("--output_path", type=str, required=True,
                       help="Output JSONL file path")
    parser.add_argument("--api_key", type=str, required=True,
                       help="DeepSeek API key")
    parser.add_argument("--model", type=str, choices=["deepseek-chat", "deepseek-reasoner"],
                       default="deepseek-reasoner", help="DeepSeek model to use (deepseek-reasoner for thinking mode)")
    parser.add_argument("--batch_size", type=int, default=5,
                       help="Batch size for concurrent processing")
    parser.add_argument("--start_id", type=int, default=0,
                       help="Starting ID for resuming processing")

    args = parser.parse_args()

    # Initialize inference client
    inference_client = DeepSeekInference(
        api_key=args.api_key,
        model_name=args.model,
        batch_size=args.batch_size
    )

    # Process dataset
    await inference_client.process_dataset(
        input_path=args.input_path,
        output_path=args.output_path,
        start_id=args.start_id
    )


if __name__ == "__main__":
    asyncio.run(main())
from together import Together
import os
import json
from dotenv import load_dotenv

load_dotenv()
client = Together()

response = client.chat.completions.create(
  model="deepseek-ai/DeepSeek-V3.1",
  messages=[
    {
        "role": "system", 
        "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Prove that the set $D$ is uncountable by constructing a surjection from $D$ to the power set of $\\mathbb{N}$"
    }
  ],
    chat_template_kwargs={"thinking": True},
    stream = True
)

# for chunk in stream:
#     delta = chunk.choices[0].delta

#     # Show reasoning tokens if present
#     if hasattr(delta, "reasoning") and delta.reasoning:
#       print(delta.reasoning, end="", flush=True)

#     # Show content tokens if present
#     if hasattr(delta, "content") and delta.content:
#       print(delta.content, end="", flush=True)

print(response.choices[0].message.content)


from together import Together
client = Together()

stream = client.chat.completions.create(
model="deepseek-ai/DeepSeek-V3.1",
messages=[
{"role": "user", "content": "What are some fun things to do in New York?"}
],
chat_template_kwargs={"thinking": True},
stream=True,
)

for chunk in stream:
delta = chunk.choices[0].delta

  # Show reasoning tokens if present
  if hasattr(delta, "reasoning") and delta.reasoning:
      print(delta.reasoning, end="", flush=True)

  # Show content tokens if present
  if hasattr(delta, "content") and delta.content:
      print(delta.content, end="", flush=True)
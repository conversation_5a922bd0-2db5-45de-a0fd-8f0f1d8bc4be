from together import Together
import os
import json
from dotenv import load_dotenv

load_dotenv()
client = Together()

response = client.chat.completions.create(
  model="deepseek-ai/DeepSeek-V3.1",
  messages=[
    {
        "role": "system", 
        "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "What are some fun things to do in New York?"
    }
  ]
)
print(response.choices[0].message.content)

import pandas as pd
import json

def read_jsonl_to_dataframe(file_path):
    """
    Read a JSONL file and convert it to a pandas DataFrame
    """
    data = []
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            data.append(json.loads(line.strip()))
    return pd.DataFrame(data)

# Load the math_500.jsonl file
df = read_jsonl_to_dataframe('math_500.jsonl')

# Display basic information about the DataFrame
print(f"DataFrame shape: {df.shape}")
print(f"\nColumn names: {list(df.columns)}")
print(f"\nData types:")
print(df.dtypes)
print(f"\nFirst few rows:")
print(df.head())

# Show some sample content from the query_and_response column
print(f"\nSample query content:")
for i in range(3):
    if i < len(df):
        query_content = df.iloc[i]['query_and_response'][0]['content']
        print(f"Row {i}: {query_content[:100]}...")
